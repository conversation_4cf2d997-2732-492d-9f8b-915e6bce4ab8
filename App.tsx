import React, { useState } from "react";
import {
  SafeAreaView,
  Text,
  TextInput,
  View,
  StyleSheet,
  ScrollView,
} from "react-native";
import { VictoryPie } from "victory-native";

const categorias = [
  { nome: "Necessidades", percentual: 55, cor: "#f94144" },
  { nome: "Diversão", percentual: 10, cor: "#f3722c" },
  { nome: "Poupança", percentual: 10, cor: "#f9c74f" },
  { nome: "Educação", percentual: 10, cor: "#90be6d" },
  { nome: "Doações", percentual: 5, cor: "#43aa8b" },
  { nome: "Liberdade financeira", percentual: 10, cor: "#577590" },
];

export default function App() {
  const [salario, setSalario] = useState("");

  const salarioFloat = parseFloat(salario.replace(",", ".")) || 0;

  const dados = categorias.map((cat) => ({
    x: cat.nome,
    y: cat.percentual,
    label: `${cat.percentual}%`,
  }));

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Regra dos 6 Potes</Text>
      <TextInput
        style={styles.input}
        placeholder="Digite seu salário"
        keyboardType="numeric"
        value={salario}
        onChangeText={setSalario}
      />
      <VictoryPie
        data={dados}
        colorScale={categorias.map((cat) => cat.cor)}
        innerRadius={50}
        labelRadius={({ innerRadius }) => (typeof innerRadius === "number" ? innerRadius : 0) + 20}
        style={{
          labels: { fill: "#fff", fontSize: 12, fontWeight: "bold" },
        }}
      />
      <ScrollView style={styles.resultado}>
        {categorias.map((cat, index) => (
          <View key={index} style={styles.item}>
            <View style={[styles.colorBox, { backgroundColor: cat.cor }]} />
            <Text style={styles.itemText}>
              {cat.nome}: R${" "}
              {((salarioFloat * cat.percentual) / 100).toFixed(2)}
            </Text>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { padding: 20, flex: 1, backgroundColor: "#fff" },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 10,
    fontSize: 18,
    marginBottom: 20,
  },
  resultado: { marginTop: 20 },
  item: { flexDirection: "row", alignItems: "center", marginBottom: 10 },
  colorBox: { width: 16, height: 16, marginRight: 8, borderRadius: 4 },
  itemText: { fontSize: 16 },
});
