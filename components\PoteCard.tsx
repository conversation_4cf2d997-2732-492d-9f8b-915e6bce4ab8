import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import * as Animatable from 'react-native-animatable';

type Props = {
  nome: string;
  valor: number;
  percentual: number;
  cor: string;
};

export default function PoteCard({ nome, valor, percentual, cor }: Props) {
  return (
    <Animatable.View animation="fadeInUp" duration={500} style={[styles.card, { borderLeftColor: cor }]}>
      <Text style={styles.nome}>{nome} ({percentual}%)</Text>
      <Text style={styles.valor}>R$ {valor.toFixed(2)}</Text>
    </Animatable.View>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginVertical: 8,
    borderLeftWidth: 5,
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  nome: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  valor: {
    fontSize: 18,
    marginTop: 4,
    color: '#374151',
  },
});
