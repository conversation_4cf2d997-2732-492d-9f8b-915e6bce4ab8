{"name": "regra-dos-6-potes", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-animatable": "^1.4.0", "react-native-pie": "^1.1.2", "react-native-safe-area-context": "^5.4.1", "react-native-svg": "^15.12.0", "victory-native": "^37.3.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}